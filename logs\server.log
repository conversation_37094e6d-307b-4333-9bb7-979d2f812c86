17:17:33.332 - INFO - [lifespan:1173] - 服务器正在启动...
17:17:33.333 - INFO - [lifespan:1178] - 🌐 Server access URLs:
17:17:33.333 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
17:17:33.335 - INFO - [lifespan:1180] -   - Network: http://*************:9080
17:17:33.335 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
17:17:33.335 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
17:17:33.335 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
17:17:33.336 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
17:17:33.336 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
17:17:33.337 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://*************:9080/metrics
17:17:33.337 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
17:17:33.337 - INFO - [lifespan:1191] -      - Health Check: http://*************:9080/health
17:17:33.337 - INFO - [lifespan:1192] -        基础健康检查
17:17:33.338 - INFO - [lifespan:1193] -      - Detailed Health: http://*************:9080/api/health/detailed
17:17:33.338 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
17:17:33.338 - INFO - [lifespan:1196] - 
  🤖 AI API:
17:17:33.338 - INFO - [lifespan:1197] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
17:17:33.338 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
17:17:33.338 - INFO - [lifespan:1199] -      - List Models: GET http://*************:9080/v1/models
17:17:33.338 - INFO - [lifespan:1200] -        获取可用模型列表
17:17:33.339 - INFO - [lifespan:1201] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
17:17:33.339 - INFO - [lifespan:1202] -        刷新模型列表
17:17:33.339 - INFO - [lifespan:1204] - 
  📈 Statistics:
17:17:33.340 - INFO - [lifespan:1205] -      - Stats Summary: http://*************:9080/api/stats/summary
17:17:33.340 - INFO - [lifespan:1206] -        24小时统计摘要
17:17:33.340 - INFO - [lifespan:1207] -      - Request Logs: http://*************:9080/api/logs/requests
17:17:33.340 - INFO - [lifespan:1208] -        请求日志API
17:17:33.340 - INFO - [lifespan:1209] -      - Error Logs: http://*************:9080/api/logs/errors
17:17:33.340 - INFO - [lifespan:1210] -        错误日志API
17:17:33.340 - INFO - [lifespan:1211] -      - Alerts: http://*************:9080/api/alerts
17:17:33.341 - INFO - [lifespan:1212] -        系统告警历史
17:17:33.341 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
17:17:33.341 - INFO - [lifespan:1215] -      base_url='http://*************:9080/v1'
17:17:33.341 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
17:17:33.341 - INFO - [lifespan:1217] - 
============================================================

17:17:33.342 - INFO - [lifespan:1222] - 已加载 85 个备用模型
17:17:33.342 - INFO - [lifespan:1231] - 服务器启动完成
17:17:33.342 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
17:17:33.587 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
17:17:33.593 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
17:17:33.618 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
17:17:33.621 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
17:18:55.336 - INFO - [add_request:794] - REQUEST_MGR: Added request 98150d66-768c-4109-8129-3826a3b6d9f4 for tracking
17:18:55.338 - INFO - [chat_completions:1481] - API [ID: 98150d66-768c-4109-8129-3826a3b6d9f4]: Created persistent request for model type 'chat'.
17:18:55.338 - INFO - [chat_completions:1496] - API [ID: 98150d66-768c-4109-8129-3826a3b6d9f4]: Returning text/event-stream response to client.
17:18:55.346 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
17:18:55.347 - INFO - [send_to_browser_task:1548] - TASK [ID: 98150d66-768c-4109-8129-3826a3b6d9f4]: Sending payload and 0 file(s) to browser.
17:18:55.348 - INFO - [send_to_browser_task:1553] - TASK [ID: 98150d66-768c-4109-8129-3826a3b6d9f4]: Payload sent and marked as sent to browser.
17:18:55.349 - INFO - [stream_generator:1597] - STREAMER [ID: 98150d66-768c-4109-8129-3826a3b6d9f4]: Generator started for model type 'chat'.
17:18:55.352 - INFO - [add_request:794] - REQUEST_MGR: Added request 255c3427-3b78-494d-82ff-d9e128a256f9 for tracking
17:18:55.352 - INFO - [chat_completions:1481] - API [ID: 255c3427-3b78-494d-82ff-d9e128a256f9]: Created persistent request for model type 'chat'.
17:18:55.352 - INFO - [chat_completions:1496] - API [ID: 255c3427-3b78-494d-82ff-d9e128a256f9]: Returning text/event-stream response to client.
17:18:55.353 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
17:18:55.355 - INFO - [send_to_browser_task:1548] - TASK [ID: 255c3427-3b78-494d-82ff-d9e128a256f9]: Sending payload and 0 file(s) to browser.
17:18:55.355 - INFO - [send_to_browser_task:1553] - TASK [ID: 255c3427-3b78-494d-82ff-d9e128a256f9]: Payload sent and marked as sent to browser.
17:18:55.356 - INFO - [stream_generator:1597] - STREAMER [ID: 255c3427-3b78-494d-82ff-d9e128a256f9]: Generator started for model type 'chat'.
17:18:58.862 - INFO - [complete_request:841] - REQUEST_MGR: Request 255c3427-3b78-494d-82ff-d9e128a256f9 completed and removed
17:18:58.893 - INFO - [stream_generator:1909] - GENERATOR [ID: 255c3427-3b78-494d-82ff-d9e128a256f9]: Cleaned up response channel.
17:18:59.882 - INFO - [complete_request:841] - REQUEST_MGR: Request 98150d66-768c-4109-8129-3826a3b6d9f4 completed and removed
17:18:59.901 - INFO - [stream_generator:1909] - GENERATOR [ID: 98150d66-768c-4109-8129-3826a3b6d9f4]: Cleaned up response channel.
17:22:33.346 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
17:27:33.337 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
17:30:41.280 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
17:30:41.281 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
17:30:41.281 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
17:30:41.282 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
17:32:19.617 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
17:32:19.617 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
17:32:20.587 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
17:32:20.587 - WARNING - [start_heartbeat:532] - 心跳超时，浏览器可能已断线
17:32:20.591 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
17:32:25.588 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
17:32:25.588 - WARNING - [start_heartbeat:532] - 心跳超时，浏览器可能已断线
17:32:25.590 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
17:32:33.337 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
17:32:41.953 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
17:32:41.953 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
17:32:41.953 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
17:32:41.955 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
17:32:42.060 - INFO - [lifespan:1238] - 生命周期: 服务器正在关闭。正在取消 6 个后台任务...
17:32:42.061 - INFO - [lifespan:1244] - 生命周期: 正在取消任务: <Task pending name='Task-3' coro=<periodic_cleanup() running at d:\aa1\lmarena-2api\proxy_server.py:713> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:32:42.061 - INFO - [lifespan:1244] - 生命周期: 正在取消任务: <Task pending name='Task-4' coro=<MonitoringAlerts.check_system_health() running at d:\aa1\lmarena-2api\proxy_server.py:578> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:32:42.062 - INFO - [lifespan:1250] - 生命周期: 等待 2 个已取消的任务完成...
17:32:42.062 - INFO - [lifespan:1256] - 生命周期: 任务 0 正常完成
17:32:42.062 - INFO - [lifespan:1256] - 生命周期: 任务 1 正常完成
17:32:42.064 - INFO - [lifespan:1258] - 生命周期: 所有后台任务已取消。关闭完成。
17:32:44.552 - INFO - [lifespan:1342] - 服务器正在启动...
17:32:44.553 - INFO - [lifespan:1347] - 🌐 Server access URLs:
17:32:44.553 - INFO - [lifespan:1348] -   - Local: http://localhost:9080
17:32:44.553 - INFO - [lifespan:1349] -   - Network: http://*************:9080
17:32:44.553 - INFO - [lifespan:1350] - 📱 Use the Network URL to access from your phone on the same WiFi
17:32:44.555 - INFO - [lifespan:1364] - 
📋 Available Endpoints:
17:32:44.555 - INFO - [lifespan:1365] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
17:32:44.555 - INFO - [lifespan:1366] -      实时监控面板，查看系统状态、请求日志、性能指标
17:32:44.555 - INFO - [lifespan:1368] - 
  📊 Metrics & Health:
17:32:44.555 - INFO - [lifespan:1369] -      - Prometheus Metrics: http://*************:9080/metrics
17:32:44.555 - INFO - [lifespan:1370] -        Prometheus格式的性能指标，可接入Grafana
17:32:44.555 - INFO - [lifespan:1371] -      - Health Check: http://*************:9080/health
17:32:44.555 - INFO - [lifespan:1372] -        基础健康检查
17:32:44.556 - INFO - [lifespan:1373] -      - Detailed Health: http://*************:9080/api/health/detailed
17:32:44.556 - INFO - [lifespan:1374] -        详细健康状态，包含评分和建议
17:32:44.556 - INFO - [lifespan:1376] - 
  🤖 AI API:
17:32:44.557 - INFO - [lifespan:1377] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
17:32:44.557 - INFO - [lifespan:1378] -        OpenAI兼容的聊天API
17:32:44.557 - INFO - [lifespan:1379] -      - List Models: GET http://*************:9080/v1/models
17:32:44.557 - INFO - [lifespan:1380] -        获取可用模型列表
17:32:44.558 - INFO - [lifespan:1381] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
17:32:44.558 - INFO - [lifespan:1382] -        刷新模型列表
17:32:44.558 - INFO - [lifespan:1384] - 
  📈 Statistics:
17:32:44.558 - INFO - [lifespan:1385] -      - Stats Summary: http://*************:9080/api/stats/summary
17:32:44.558 - INFO - [lifespan:1386] -        24小时统计摘要
17:32:44.558 - INFO - [lifespan:1387] -      - Request Logs: http://*************:9080/api/logs/requests
17:32:44.559 - INFO - [lifespan:1388] -        请求日志API
17:32:44.559 - INFO - [lifespan:1389] -      - Error Logs: http://*************:9080/api/logs/errors
17:32:44.559 - INFO - [lifespan:1390] -        错误日志API
17:32:44.560 - INFO - [lifespan:1391] -      - Alerts: http://*************:9080/api/alerts
17:32:44.560 - INFO - [lifespan:1392] -        系统告警历史
17:32:44.560 - INFO - [lifespan:1394] - 
  🛠️  OpenAI Client Config:
17:32:44.560 - INFO - [lifespan:1395] -      base_url='http://*************:9080/v1'
17:32:44.560 - INFO - [lifespan:1396] -      api_key='sk-any-string-you-like'
17:32:44.561 - INFO - [lifespan:1397] - 
============================================================

17:32:44.561 - INFO - [lifespan:1402] - 已加载 85 个备用模型
17:32:44.561 - INFO - [lifespan:1411] - 服务器启动完成
17:32:44.562 - INFO - [periodic_cleanup:877] - 清理任务执行完成. 活跃请求: 0
17:32:47.611 - INFO - [websocket_endpoint:1463] - ✅ 浏览器WebSocket已连接
17:32:47.617 - INFO - [websocket_endpoint:1498] - 🤝 收到重连握手，浏览器有 0 个待处理请求
17:32:47.643 - INFO - [websocket_endpoint:1463] - ✅ 浏览器WebSocket已连接
17:32:47.646 - INFO - [websocket_endpoint:1498] - 🤝 收到重连握手，浏览器有 0 个待处理请求
17:37:44.549 - INFO - [periodic_cleanup:877] - 清理任务执行完成. 活跃请求: 0
17:40:05.112 - WARNING - [websocket_endpoint:1566] - ❌ 浏览器客户端已断开连接
17:40:05.114 - INFO - [websocket_endpoint:1586] - WebSocket cleaned up. Persistent requests kept alive.
17:40:05.114 - WARNING - [websocket_endpoint:1566] - ❌ 浏览器客户端已断开连接
17:40:05.114 - INFO - [websocket_endpoint:1586] - WebSocket cleaned up. Persistent requests kept alive.
17:40:05.221 - INFO - [lifespan:1418] - 生命周期: 服务器正在关闭。正在取消 4 个后台任务...
17:40:05.221 - INFO - [lifespan:1424] - 生命周期: 正在取消任务: <Task pending name='Task-4' coro=<MonitoringAlerts.check_system_health() running at d:\aa1\lmarena-2api\proxy_server.py:747> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:40:05.222 - INFO - [lifespan:1424] - 生命周期: 正在取消任务: <Task pending name='Task-21' coro=<WebSocketHeartbeat.start_heartbeat() running at d:\aa1\lmarena-2api\proxy_server.py:709> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:40:05.222 - INFO - [lifespan:1424] - 生命周期: 正在取消任务: <Task pending name='Task-3' coro=<periodic_cleanup() running at d:\aa1\lmarena-2api\proxy_server.py:882> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:40:05.222 - INFO - [lifespan:1424] - 生命周期: 正在取消任务: <Task pending name='Task-28' coro=<WebSocketHeartbeat.start_heartbeat() running at d:\aa1\lmarena-2api\proxy_server.py:709> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:40:05.223 - INFO - [lifespan:1430] - 生命周期: 等待 4 个已取消的任务完成...
17:40:05.223 - INFO - [lifespan:1436] - 生命周期: 任务 0 正常完成
17:40:05.223 - INFO - [lifespan:1436] - 生命周期: 任务 1 正常完成
17:40:05.223 - INFO - [lifespan:1436] - 生命周期: 任务 2 正常完成
17:40:05.224 - INFO - [lifespan:1436] - 生命周期: 任务 3 正常完成
17:40:05.224 - INFO - [lifespan:1443] - 生命周期: 所有后台任务已取消。关闭完成。
17:40:07.621 - INFO - [lifespan:1342] - 服务器正在启动...
17:40:07.622 - INFO - [lifespan:1347] - 🌐 Server access URLs:
17:40:07.623 - INFO - [lifespan:1348] -   - Local: http://localhost:9080
17:40:07.623 - INFO - [lifespan:1349] -   - Network: http://*************:9080
17:40:07.623 - INFO - [lifespan:1350] - 📱 Use the Network URL to access from your phone on the same WiFi
17:40:07.623 - INFO - [lifespan:1364] - 
📋 Available Endpoints:
17:40:07.623 - INFO - [lifespan:1365] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
17:40:07.625 - INFO - [lifespan:1366] -      实时监控面板，查看系统状态、请求日志、性能指标
17:40:07.625 - INFO - [lifespan:1368] - 
  📊 Metrics & Health:
17:40:07.625 - INFO - [lifespan:1369] -      - Prometheus Metrics: http://*************:9080/metrics
17:40:07.625 - INFO - [lifespan:1370] -        Prometheus格式的性能指标，可接入Grafana
17:40:07.625 - INFO - [lifespan:1371] -      - Health Check: http://*************:9080/health
17:40:07.625 - INFO - [lifespan:1372] -        基础健康检查
17:40:07.625 - INFO - [lifespan:1373] -      - Detailed Health: http://*************:9080/api/health/detailed
17:40:07.625 - INFO - [lifespan:1374] -        详细健康状态，包含评分和建议
17:40:07.626 - INFO - [lifespan:1376] - 
  🤖 AI API:
17:40:07.626 - INFO - [lifespan:1377] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
17:40:07.626 - INFO - [lifespan:1378] -        OpenAI兼容的聊天API
17:40:07.626 - INFO - [lifespan:1379] -      - List Models: GET http://*************:9080/v1/models
17:40:07.626 - INFO - [lifespan:1380] -        获取可用模型列表
17:40:07.627 - INFO - [lifespan:1381] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
17:40:07.627 - INFO - [lifespan:1382] -        刷新模型列表
17:40:07.627 - INFO - [lifespan:1384] - 
  📈 Statistics:
17:40:07.627 - INFO - [lifespan:1385] -      - Stats Summary: http://*************:9080/api/stats/summary
17:40:07.628 - INFO - [lifespan:1386] -        24小时统计摘要
17:40:07.628 - INFO - [lifespan:1387] -      - Request Logs: http://*************:9080/api/logs/requests
17:40:07.628 - INFO - [lifespan:1388] -        请求日志API
17:40:07.628 - INFO - [lifespan:1389] -      - Error Logs: http://*************:9080/api/logs/errors
17:40:07.628 - INFO - [lifespan:1390] -        错误日志API
17:40:07.628 - INFO - [lifespan:1391] -      - Alerts: http://*************:9080/api/alerts
17:40:07.628 - INFO - [lifespan:1392] -        系统告警历史
17:40:07.629 - INFO - [lifespan:1394] - 
  🛠️  OpenAI Client Config:
17:40:07.629 - INFO - [lifespan:1395] -      base_url='http://*************:9080/v1'
17:40:07.629 - INFO - [lifespan:1396] -      api_key='sk-any-string-you-like'
17:40:07.629 - INFO - [lifespan:1397] - 
============================================================

17:40:07.629 - INFO - [lifespan:1402] - 已加载 85 个备用模型
17:40:07.629 - INFO - [lifespan:1411] - 服务器启动完成
17:40:07.630 - INFO - [periodic_cleanup:877] - 清理任务执行完成. 活跃请求: 0
17:40:10.604 - INFO - [websocket_endpoint:1463] - ✅ 浏览器WebSocket已连接
17:40:10.606 - INFO - [websocket_endpoint:1498] - 🤝 收到重连握手，浏览器有 0 个待处理请求
17:40:10.631 - INFO - [websocket_endpoint:1463] - ✅ 浏览器WebSocket已连接
17:40:10.635 - INFO - [websocket_endpoint:1498] - 🤝 收到重连握手，浏览器有 0 个待处理请求
