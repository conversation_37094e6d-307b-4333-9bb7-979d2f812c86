# Ngrok 内网穿透配置指南

## 🌐 什么是 Ngrok？

Ngrok 是一个内网穿透工具，可以将您本地运行的服务暴露到公网，让您可以从任何地方访问您的 LMArena 代理服务器。

## 📋 功能特性

- ✅ **一键启动**：通过 Web 界面或 API 轻松启动/停止隧道
- ✅ **自动配置**：自动处理认证令牌和区域设置
- ✅ **状态监控**：实时显示隧道状态和公网地址
- ✅ **安全可靠**：支持 HTTPS 加密传输
- ✅ **多平台支持**：Windows、macOS、Linux 全平台支持

## 🚀 快速开始

### 1. 安装 Ngrok

#### Windows
```bash
# 方法1：从官网下载
# 1. 访问 https://ngrok.com/download
# 2. 下载 Windows 版本
# 3. 解压到任意目录（如 C:\ngrok\）
# 4. 将目录添加到系统 PATH 环境变量

# 方法2：使用 Chocolatey
choco install ngrok

# 方法3：使用 Scoop
scoop install ngrok
```

#### macOS
```bash
# 方法1：使用 Homebrew
brew install ngrok/ngrok/ngrok

# 方法2：从官网下载
# 访问 https://ngrok.com/download 下载 macOS 版本
```

#### Linux
```bash
# Ubuntu/Debian
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
sudo apt update && sudo apt install ngrok

# 或从官网下载
# 访问 https://ngrok.com/download 下载 Linux 版本
```

### 2. 获取认证令牌

1. 访问 [ngrok.com](https://ngrok.com) 并注册账户
2. 登录后访问 [Dashboard](https://dashboard.ngrok.com/get-started/your-authtoken)
3. 复制您的认证令牌（Authtoken）

### 3. 配置 LMArena 代理

#### 方法1：通过监控面板配置
1. 启动 LMArena 代理服务器
2. 访问监控面板：`http://localhost:9080/monitor`
3. 点击"系统设置"标签
4. 在"Ngrok 内网穿透"部分：
   - 启用 Ngrok：勾选"启用 Ngrok"
   - 输入认证令牌：粘贴您的 authtoken
   - 选择区域：选择离您最近的区域
   - （可选）自定义子域名：需要付费账户
5. 点击"保存配置"

#### 方法2：通过配置文件
编辑 `logs/config.json` 文件：
```json
{
  "ngrok": {
    "enabled": true,
    "authtoken": "您的认证令牌",
    "subdomain": "您的自定义子域名（可选）",
    "region": "us"
  }
}
```

### 4. 启动隧道

#### 自动启动（推荐）
如果在配置中启用了 Ngrok，服务器启动时会自动创建隧道。

#### 手动启动
1. 通过监控面板：点击"启动 Ngrok"按钮
2. 通过 API：`POST /api/ngrok/start`

## 🔧 配置选项

### 区域设置
选择离您最近的区域以获得最佳性能：
- `us` - 美国（默认）
- `eu` - 欧洲
- `ap` - 亚太
- `au` - 澳大利亚
- `sa` - 南美
- `jp` - 日本
- `in` - 印度

### 自定义子域名
付费账户可以设置自定义子域名，例如：
- 设置 `subdomain: "my-lmarena"`
- 获得固定地址：`https://my-lmarena.ngrok.io`

## 📊 监控和管理

### 状态查看
- **监控面板**：实时显示隧道状态
- **API 端点**：`GET /api/ngrok/status`

### 隧道控制
- **启动隧道**：`POST /api/ngrok/start`
- **停止隧道**：`POST /api/ngrok/stop`
- **重启隧道**：`POST /api/ngrok/restart`

### 安装检查
- **检查安装**：`GET /api/ngrok/install-guide`

## 🛡️ 安全注意事项

1. **保护认证令牌**：不要在公开场所分享您的 authtoken
2. **访问控制**：考虑在 ngrok 隧道前添加身份验证
3. **监控访问**：定期检查 ngrok 仪表板的访问日志
4. **限制暴露**：只在需要时启用公网访问

## 🔍 故障排除

### 常见问题

#### 1. "Ngrok 未安装"
**解决方案**：
- 确保 ngrok 已正确安装并添加到 PATH
- 在命令行运行 `ngrok version` 验证安装

#### 2. "认证失败"
**解决方案**：
- 检查 authtoken 是否正确
- 确保已在 ngrok 官网注册账户

#### 3. "隧道启动失败"
**解决方案**：
- 检查网络连接
- 确认端口 9080 未被其他程序占用
- 查看服务器日志获取详细错误信息

#### 4. "子域名不可用"
**解决方案**：
- 确保您有付费账户（免费账户不支持自定义子域名）
- 尝试不同的子域名

### 日志查看
服务器日志会记录 ngrok 相关的操作和错误信息：
```
[INFO] 🚀 正在启动 Ngrok 内网穿透...
[INFO] 🌍 Ngrok 公网地址: https://abc123.ngrok.io
[ERROR] 启动 ngrok 隧道失败: authentication failed
```

## 📞 技术支持

如果遇到问题，请：
1. 查看服务器日志文件
2. 检查 ngrok 官方文档
3. 在项目 GitHub 页面提交 Issue

## 🔗 相关链接

- [Ngrok 官网](https://ngrok.com)
- [Ngrok 文档](https://ngrok.com/docs)
- [Ngrok 定价](https://ngrok.com/pricing)
- [LMArena 项目](https://lmarena.ai)
