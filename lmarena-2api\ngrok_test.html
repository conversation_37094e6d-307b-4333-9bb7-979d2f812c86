<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ngrok 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .control-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .config-section {
            background: #f3e5f5;
            padding: 20px;
            border-radius: 8px;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #4338ca;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        input, select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            font-size: 14px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-badge.success {
            background: #d1fae5;
            color: #059669;
        }
        .status-badge.failed {
            background: #fee2e2;
            color: #dc2626;
        }
        .status-badge.warning {
            background: #fef3c7;
            color: #d97706;
        }
        .url-display {
            font-family: monospace;
            background: #f1f5f9;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Ngrok 内网穿透测试</h1>
        
        <!-- 状态显示 -->
        <div class="status-section">
            <h3>📊 当前状态</h3>
            <p>状态: <span id="ngrok-status" class="status-badge warning">检查中...</span></p>
            <p>公网地址: <span id="ngrok-url" class="url-display">暂无</span></p>
            <button onclick="loadStatus()">刷新状态</button>
        </div>
        
        <!-- 控制按钮 -->
        <div class="control-section">
            <h3>🎮 隧道控制</h3>
            <button id="start-btn" onclick="startTunnel()" disabled>启动隧道</button>
            <button id="stop-btn" onclick="stopTunnel()" disabled>停止隧道</button>
            <button id="restart-btn" onclick="restartTunnel()" disabled>重启隧道</button>
        </div>
        
        <!-- 配置设置 -->
        <div class="config-section">
            <h3>⚙️ 配置设置</h3>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enabled"> 启用 Ngrok
                </label>
            </div>
            
            <div class="form-group">
                <label for="authtoken">认证令牌:</label>
                <input type="password" id="authtoken" placeholder="从 ngrok.com 获取" style="width: 300px;">
            </div>
            
            <div class="form-group">
                <label for="region">服务器区域:</label>
                <select id="region">
                    <option value="us">美国 (us)</option>
                    <option value="eu">欧洲 (eu)</option>
                    <option value="ap">亚太 (ap)</option>
                    <option value="au">澳大利亚 (au)</option>
                    <option value="sa">南美 (sa)</option>
                    <option value="jp">日本 (jp)</option>
                    <option value="in">印度 (in)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="subdomain">自定义子域名 (可选):</label>
                <input type="text" id="subdomain" placeholder="需要付费账户">
            </div>
            
            <button onclick="saveConfig()">保存配置</button>
            <button onclick="loadConfig()">加载配置</button>
        </div>
    </div>

    <script>
        // 加载状态
        async function loadStatus() {
            try {
                const response = await fetch('/api/ngrok/status');
                const status = await response.json();
                
                const statusElement = document.getElementById('ngrok-status');
                const urlElement = document.getElementById('ngrok-url');
                const startBtn = document.getElementById('start-btn');
                const stopBtn = document.getElementById('stop-btn');
                const restartBtn = document.getElementById('restart-btn');
                
                if (!status.installed) {
                    statusElement.textContent = '未安装';
                    statusElement.className = 'status-badge failed';
                    urlElement.textContent = '请先安装 ngrok';
                    startBtn.disabled = true;
                    stopBtn.disabled = true;
                    restartBtn.disabled = true;
                } else if (status.running) {
                    statusElement.textContent = '运行中';
                    statusElement.className = 'status-badge success';
                    urlElement.textContent = status.public_url || '获取中...';
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                    restartBtn.disabled = false;
                } else {
                    statusElement.textContent = '已停止';
                    statusElement.className = 'status-badge warning';
                    urlElement.textContent = '暂无';
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    restartBtn.disabled = true;
                }
            } catch (error) {
                console.error('加载状态失败:', error);
                document.getElementById('ngrok-status').textContent = '检查失败';
                document.getElementById('ngrok-status').className = 'status-badge failed';
            }
        }
        
        // 启动隧道
        async function startTunnel() {
            try {
                const response = await fetch('/api/ngrok/start', { method: 'POST' });
                const result = await response.json();
                
                if (response.ok) {
                    alert('隧道启动成功！\n公网地址: ' + result.public_url);
                    loadStatus();
                } else {
                    alert('启动失败: ' + result.detail);
                }
            } catch (error) {
                alert('启动失败: ' + error.message);
            }
        }
        
        // 停止隧道
        async function stopTunnel() {
            try {
                const response = await fetch('/api/ngrok/stop', { method: 'POST' });
                if (response.ok) {
                    alert('隧道已停止');
                    loadStatus();
                } else {
                    const result = await response.json();
                    alert('停止失败: ' + result.detail);
                }
            } catch (error) {
                alert('停止失败: ' + error.message);
            }
        }
        
        // 重启隧道
        async function restartTunnel() {
            try {
                const response = await fetch('/api/ngrok/restart', { method: 'POST' });
                const result = await response.json();
                
                if (response.ok) {
                    alert('隧道重启成功！\n公网地址: ' + result.public_url);
                    loadStatus();
                } else {
                    alert('重启失败: ' + result.detail);
                }
            } catch (error) {
                alert('重启失败: ' + error.message);
            }
        }
        
        // 保存配置
        async function saveConfig() {
            try {
                const config = {
                    ngrok: {
                        enabled: document.getElementById('enabled').checked,
                        authtoken: document.getElementById('authtoken').value.trim() || null,
                        region: document.getElementById('region').value,
                        subdomain: document.getElementById('subdomain').value.trim() || null
                    }
                };
                
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(config)
                });
                
                if (response.ok) {
                    alert('配置保存成功');
                } else {
                    alert('配置保存失败');
                }
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        }
        
        // 加载配置
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const config = await response.json();
                const ngrokConfig = config.ngrok || {};
                
                document.getElementById('enabled').checked = ngrokConfig.enabled || false;
                document.getElementById('authtoken').value = ngrokConfig.authtoken || '';
                document.getElementById('region').value = ngrokConfig.region || 'us';
                document.getElementById('subdomain').value = ngrokConfig.subdomain || '';
            } catch (error) {
                console.error('加载配置失败:', error);
            }
        }
        
        // 页面加载时初始化
        window.onload = function() {
            loadStatus();
            loadConfig();
        };
    </script>
</body>
</html>
