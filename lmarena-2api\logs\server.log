17:42:01.464 - INFO - [lifespan:1342] - 服务器正在启动...
17:42:01.465 - INFO - [lifespan:1347] - 🌐 Server access URLs:
17:42:01.465 - INFO - [lifespan:1348] -   - Local: http://localhost:9080
17:42:01.465 - INFO - [lifespan:1349] -   - Network: http://*************:9080
17:42:01.466 - INFO - [lifespan:1350] - 📱 Use the Network URL to access from your phone on the same WiFi
17:42:01.466 - INFO - [lifespan:1364] - 
📋 Available Endpoints:
17:42:01.466 - INFO - [lifespan:1365] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
17:42:01.467 - INFO - [lifespan:1366] -      实时监控面板，查看系统状态、请求日志、性能指标
17:42:01.467 - INFO - [lifespan:1368] - 
  📊 Metrics & Health:
17:42:01.467 - INFO - [lifespan:1369] -      - Prometheus Metrics: http://*************:9080/metrics
17:42:01.468 - INFO - [lifespan:1370] -        Prometheus格式的性能指标，可接入Grafana
17:42:01.468 - INFO - [lifespan:1371] -      - Health Check: http://*************:9080/health
17:42:01.468 - INFO - [lifespan:1372] -        基础健康检查
17:42:01.468 - INFO - [lifespan:1373] -      - Detailed Health: http://*************:9080/api/health/detailed
17:42:01.468 - INFO - [lifespan:1374] -        详细健康状态，包含评分和建议
17:42:01.468 - INFO - [lifespan:1376] - 
  🤖 AI API:
17:42:01.470 - INFO - [lifespan:1377] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
17:42:01.470 - INFO - [lifespan:1378] -        OpenAI兼容的聊天API
17:42:01.470 - INFO - [lifespan:1379] -      - List Models: GET http://*************:9080/v1/models
17:42:01.470 - INFO - [lifespan:1380] -        获取可用模型列表
17:42:01.470 - INFO - [lifespan:1381] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
17:42:01.471 - INFO - [lifespan:1382] -        刷新模型列表
17:42:01.471 - INFO - [lifespan:1384] - 
  📈 Statistics:
17:42:01.471 - INFO - [lifespan:1385] -      - Stats Summary: http://*************:9080/api/stats/summary
17:42:01.471 - INFO - [lifespan:1386] -        24小时统计摘要
17:42:01.471 - INFO - [lifespan:1387] -      - Request Logs: http://*************:9080/api/logs/requests
17:42:01.472 - INFO - [lifespan:1388] -        请求日志API
17:42:01.472 - INFO - [lifespan:1389] -      - Error Logs: http://*************:9080/api/logs/errors
17:42:01.472 - INFO - [lifespan:1390] -        错误日志API
17:42:01.472 - INFO - [lifespan:1391] -      - Alerts: http://*************:9080/api/alerts
17:42:01.472 - INFO - [lifespan:1392] -        系统告警历史
17:42:01.472 - INFO - [lifespan:1394] - 
  🛠️  OpenAI Client Config:
17:42:01.472 - INFO - [lifespan:1395] -      base_url='http://*************:9080/v1'
17:42:01.472 - INFO - [lifespan:1396] -      api_key='sk-any-string-you-like'
17:42:01.472 - INFO - [lifespan:1397] - 
============================================================

17:42:01.472 - INFO - [lifespan:1402] - 已加载 85 个备用模型
17:42:01.473 - INFO - [lifespan:1411] - 服务器启动完成
17:42:01.473 - INFO - [periodic_cleanup:877] - 清理任务执行完成. 活跃请求: 0
17:42:01.474 - INFO - [lifespan:1418] - 生命周期: 服务器正在关闭。正在取消 2 个后台任务...
17:42:01.475 - INFO - [lifespan:1424] - 生命周期: 正在取消任务: <Task pending name='Task-4' coro=<MonitoringAlerts.check_system_health() running at D:\aa1\lmarena-2api\proxy_server.py:747> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:42:01.475 - INFO - [lifespan:1424] - 生命周期: 正在取消任务: <Task pending name='Task-3' coro=<periodic_cleanup() running at D:\aa1\lmarena-2api\proxy_server.py:882> wait_for=<Future pending cb=[Task.task_wakeup()]>>
17:42:01.475 - INFO - [lifespan:1430] - 生命周期: 等待 2 个已取消的任务完成...
17:42:01.475 - INFO - [lifespan:1436] - 生命周期: 任务 0 正常完成
17:42:01.477 - INFO - [lifespan:1436] - 生命周期: 任务 1 正常完成
17:42:01.477 - INFO - [lifespan:1443] - 生命周期: 所有后台任务已取消。关闭完成。
