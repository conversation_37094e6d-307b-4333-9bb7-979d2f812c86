#!/bin/bash

# LMArena Ngrok 配置助手脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo
echo "🚀 LMArena Ngrok 配置助手"
echo "========================================"
echo

# 检查是否在正确的目录
if [ ! -f "proxy_server.py" ]; then
    print_error "请在 lmarena-2api 项目根目录下运行此脚本"
    exit 1
fi

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "Python 未安装或不在 PATH 中"
    print_info "请先安装 Python 3.8+ 后再运行此脚本"
    exit 1
fi

# 确定 Python 命令
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

print_success "Python 已安装"

# 检查依赖是否安装
print_info "检查依赖..."
if ! $PYTHON_CMD -c "import pyngrok" &> /dev/null; then
    print_warning "pyngrok 未安装，正在安装..."
    pip3 install pyngrok || pip install pyngrok
    if [ $? -eq 0 ]; then
        print_success "pyngrok 安装成功"
    else
        print_error "安装 pyngrok 失败"
        exit 1
    fi
else
    print_success "pyngrok 已安装"
fi

# 检查 ngrok 是否安装
print_info "检查 ngrok 安装状态..."
if ! command -v ngrok &> /dev/null; then
    print_error "Ngrok 未安装"
    echo
    print_info "请按以下步骤安装 ngrok："
    
    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macOS 安装方法："
        echo "1. 使用 Homebrew: brew install ngrok/ngrok/ngrok"
        echo "2. 或访问 https://ngrok.com/download 手动下载"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "Linux 安装方法："
        echo "# Ubuntu/Debian"
        echo "curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null"
        echo "echo \"deb https://ngrok-agent.s3.amazonaws.com buster main\" | sudo tee /etc/apt/sources.list.d/ngrok.list"
        echo "sudo apt update && sudo apt install ngrok"
        echo
        echo "# 或访问 https://ngrok.com/download 手动下载"
    else
        echo "请访问 https://ngrok.com/download 下载适合您系统的版本"
    fi
    echo
    exit 1
fi

print_success "Ngrok 已安装"

# 运行 Python 配置脚本
echo
print_info "启动配置向导..."
$PYTHON_CMD setup_ngrok.py

if [ $? -eq 0 ]; then
    echo
    print_success "配置完成！"
    echo
    print_info "下一步："
    echo "1. 运行 '$PYTHON_CMD proxy_server.py' 启动服务器"
    echo "2. 访问监控面板查看 ngrok 状态"
    echo "3. 使用公网地址访问您的服务"
    echo
else
    print_error "配置失败"
    exit 1
fi
