@echo off
chcp 65001 >nul
title LMArena Ngrok 配置助手

echo.
echo 🚀 LMArena Ngrok 配置助手
echo ========================================
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或不在 PATH 中
    echo 请先安装 Python 3.8+ 后再运行此脚本
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "proxy_server.py" (
    echo ❌ 请在 lmarena-2api 项目根目录下运行此脚本
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 📦 检查依赖...
python -c "import pyngrok" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ pyngrok 未安装，正在安装...
    pip install pyngrok
    if errorlevel 1 (
        echo ❌ 安装 pyngrok 失败
        pause
        exit /b 1
    )
    echo ✅ pyngrok 安装成功
)

REM 检查 ngrok 是否安装
echo 🔍 检查 ngrok 安装状态...
ngrok version >nul 2>&1
if errorlevel 1 (
    echo ❌ Ngrok 未安装
    echo.
    echo 📦 请按以下步骤安装 ngrok：
    echo 1. 访问 https://ngrok.com/download
    echo 2. 下载 Windows 版本的 ngrok
    echo 3. 解压到任意目录（如 C:\ngrok\）
    echo 4. 将 ngrok.exe 所在目录添加到系统 PATH 环境变量
    echo 5. 重启命令行窗口
    echo.
    echo 或使用包管理器：
    echo - Chocolatey: choco install ngrok
    echo - Scoop: scoop install ngrok
    echo.
    pause
    exit /b 1
)

echo ✅ Ngrok 已安装

REM 运行 Python 配置脚本
echo.
echo 🔧 启动配置向导...
python setup_ngrok.py

if errorlevel 1 (
    echo ❌ 配置失败
    pause
    exit /b 1
)

echo.
echo 🎉 配置完成！
echo.
echo 📋 下一步：
echo 1. 运行 'python proxy_server.py' 启动服务器
echo 2. 访问监控面板查看 ngrok 状态
echo 3. 使用公网地址访问您的服务
echo.
pause
