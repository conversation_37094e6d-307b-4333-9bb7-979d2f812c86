#!/usr/bin/env python3
"""
测试 Ngrok 功能的脚本
"""

import requests
import json
import time

def test_ngrok_api():
    """测试 ngrok API 功能"""
    base_url = "http://localhost:9080"
    
    print("🧪 测试 Ngrok API 功能")
    print("=" * 40)
    
    # 1. 测试获取状态
    print("\n1. 测试获取 Ngrok 状态...")
    try:
        response = requests.get(f"{base_url}/api/ngrok/status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 状态获取成功:")
            print(f"   - 已安装: {status.get('installed', False)}")
            print(f"   - 运行中: {status.get('running', False)}")
            print(f"   - 公网地址: {status.get('public_url', 'N/A')}")
        else:
            print(f"❌ 状态获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 2. 测试获取安装指南
    print("\n2. 测试获取安装指南...")
    try:
        response = requests.get(f"{base_url}/api/ngrok/install-guide")
        if response.status_code == 200:
            guide = response.json()
            print(f"✅ 安装指南获取成功:")
            print(f"   - 已安装: {guide.get('installed', False)}")
            print(f"   - 系统: {guide.get('system', 'Unknown')}")
        else:
            print(f"❌ 安装指南获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 3. 测试配置保存
    print("\n3. 测试配置保存...")
    try:
        config = {
            "ngrok": {
                "enabled": True,
                "authtoken": "test-token",
                "region": "us",
                "subdomain": "test-subdomain"
            }
        }
        
        response = requests.post(
            f"{base_url}/api/config",
            headers={"Content-Type": "application/json"},
            data=json.dumps(config)
        )
        
        if response.status_code == 200:
            print("✅ 配置保存成功")
        else:
            print(f"❌ 配置保存失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 4. 测试配置读取
    print("\n4. 测试配置读取...")
    try:
        response = requests.get(f"{base_url}/api/config")
        if response.status_code == 200:
            config = response.json()
            ngrok_config = config.get('ngrok', {})
            print(f"✅ 配置读取成功:")
            print(f"   - 启用: {ngrok_config.get('enabled', False)}")
            print(f"   - 区域: {ngrok_config.get('region', 'N/A')}")
            print(f"   - 子域名: {ngrok_config.get('subdomain', 'N/A')}")
        else:
            print(f"❌ 配置读取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 测试完成！")

def test_monitor_page():
    """测试监控页面是否包含 ngrok 功能"""
    print("\n🌐 测试监控页面...")
    
    try:
        response = requests.get("http://localhost:9080/monitor")
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含 ngrok 相关内容
            checks = [
                ("内网穿透", "内网穿透标题"),
                ("ngrok-status", "Ngrok 状态元素"),
                ("startNgrok", "启动 Ngrok 函数"),
                ("stopNgrok", "停止 Ngrok 函数"),
                ("saveNgrokSettings", "保存 Ngrok 设置函数"),
                ("loadNgrokStatus", "加载 Ngrok 状态函数")
            ]
            
            print("✅ 监控页面加载成功")
            print("检查 Ngrok 功能集成:")
            
            for keyword, description in checks:
                if keyword in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description}")
        else:
            print(f"❌ 监控页面加载失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    print("🚀 LMArena Ngrok 功能测试")
    print("请确保服务器正在运行 (python proxy_server.py)")
    print()
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 测试 API
    test_ngrok_api()
    
    # 测试监控页面
    test_monitor_page()
    
    print("\n📋 下一步:")
    print("1. 访问 http://localhost:9080/monitor")
    print("2. 点击'系统设置'标签")
    print("3. 查看'内网穿透 (Ngrok)'部分")
    print("4. 配置您的 ngrok 认证令牌")
    print("5. 点击'启动隧道'按钮")
