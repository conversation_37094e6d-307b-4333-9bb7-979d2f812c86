# 🌐 LMArena 代理服务器 - Ngrok 内网穿透功能

## 📋 功能概述

本项目已集成 Ngrok 内网穿透功能，让您可以将本地运行的 LMArena 代理服务器暴露到公网，实现从任何地方访问。

### ✨ 主要特性

- 🚀 **一键启动**：通过配置文件或 Web 界面轻松启动 ngrok 隧道
- 🔧 **灵活配置**：支持自定义区域、子域名等高级选项
- 📊 **状态监控**：实时显示隧道状态和公网地址
- 🛡️ **安全可靠**：支持 HTTPS 加密传输
- 🌍 **全球访问**：支持多个区域节点，选择最佳连接

## 🚀 快速开始

### 1. 安装依赖

确保已安装 pyngrok：

```bash
pip install -r requirements.txt
```

### 2. 安装 Ngrok

#### Windows
```bash
# 方法1：从官网下载
# 访问 https://ngrok.com/download 下载并解压到 PATH

# 方法2：使用包管理器
choco install ngrok
# 或
scoop install ngrok
```

#### macOS
```bash
brew install ngrok/ngrok/ngrok
```

#### Linux
```bash
# Ubuntu/Debian
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
sudo apt update && sudo apt install ngrok
```

### 3. 获取认证令牌

1. 访问 [ngrok.com](https://ngrok.com) 注册账户
2. 登录后访问 [Dashboard](https://dashboard.ngrok.com/get-started/your-authtoken)
3. 复制您的认证令牌

### 4. 配置 Ngrok

#### 方法1：使用配置助手（推荐）

```bash
python setup_ngrok.py
```

按照提示输入认证令牌和配置选项。

#### 方法2：手动配置

编辑 `logs/config.json` 文件：

```json
{
  "ngrok": {
    "enabled": true,
    "authtoken": "您的认证令牌",
    "region": "us",
    "subdomain": "my-lmarena"
  }
}
```

#### 方法3：通过监控面板配置

1. 启动服务器：`python proxy_server.py`
2. 访问监控面板：`http://localhost:9080/monitor`
3. 在"系统设置"中配置 Ngrok

### 5. 启动服务

```bash
python proxy_server.py
```

如果配置正确，您将看到类似输出：

```
🚀 正在启动 Ngrok 内网穿透...
🌍 Ngrok 公网地址: https://abc123.ngrok.io
🔗 您可以通过此地址从任何地方访问服务
```

## 🔧 配置选项

### 基本配置

| 选项 | 说明 | 默认值 |
|------|------|--------|
| `enabled` | 是否启用 ngrok | `false` |
| `authtoken` | ngrok 认证令牌 | `null` |
| `region` | 服务器区域 | `"us"` |
| `subdomain` | 自定义子域名 | `null` |

### 区域选择

选择离您最近的区域以获得最佳性能：

- `us` - 美国（默认）
- `eu` - 欧洲
- `ap` - 亚太
- `au` - 澳大利亚
- `sa` - 南美
- `jp` - 日本
- `in` - 印度

### 自定义子域名

付费账户可以设置自定义子域名：

```json
{
  "ngrok": {
    "subdomain": "my-lmarena"
  }
}
```

这将获得固定地址：`https://my-lmarena.ngrok.io`

## 📊 API 接口

### 获取状态

```bash
GET /api/ngrok/status
```

返回：
```json
{
  "installed": true,
  "running": true,
  "public_url": "https://abc123.ngrok.io",
  "tunnel_info": {
    "name": "http-9080",
    "proto": "https",
    "config": {...}
  }
}
```

### 启动隧道

```bash
POST /api/ngrok/start
```

### 停止隧道

```bash
POST /api/ngrok/stop
```

### 重启隧道

```bash
POST /api/ngrok/restart
```

### 获取安装指南

```bash
GET /api/ngrok/install-guide
```

## 🛠️ 使用示例

### 通过公网地址访问

一旦 ngrok 隧道启动，您可以使用公网地址访问所有功能：

```python
from openai import OpenAI

# 使用 ngrok 公网地址
client = OpenAI(
    base_url="https://abc123.ngrok.io/v1",
    api_key="sk-any-string"
)

response = client.chat.completions.create(
    model="claude-3-5-sonnet-20241022",
    messages=[{"role": "user", "content": "Hello!"}]
)
```

### 监控面板访问

- 本地：`http://localhost:9080/monitor`
- 公网：`https://abc123.ngrok.io/monitor`

## 🔍 故障排除

### 常见问题

1. **"Ngrok 未安装"**
   - 确保 ngrok 已安装并在 PATH 中
   - 运行 `ngrok version` 验证

2. **"认证失败"**
   - 检查 authtoken 是否正确
   - 确保已在 ngrok 官网注册

3. **"隧道启动失败"**
   - 检查网络连接
   - 查看服务器日志获取详细错误

4. **"子域名不可用"**
   - 确保您有付费账户
   - 尝试不同的子域名

### 日志查看

服务器日志会记录 ngrok 相关操作：

```
[INFO] 🚀 正在启动 Ngrok 内网穿透...
[INFO] 🌍 Ngrok 公网地址: https://abc123.ngrok.io
[ERROR] 启动 ngrok 隧道失败: authentication failed
```

## 🛡️ 安全注意事项

1. **保护认证令牌**：不要在公开场所分享您的 authtoken
2. **访问控制**：考虑在应用层添加身份验证
3. **监控访问**：定期检查 ngrok 仪表板的访问日志
4. **限制暴露**：只在需要时启用公网访问

## 📞 技术支持

如果遇到问题：

1. 查看服务器日志文件
2. 检查 [Ngrok 官方文档](https://ngrok.com/docs)
3. 在项目 GitHub 页面提交 Issue

## 🔗 相关链接

- [Ngrok 官网](https://ngrok.com)
- [Ngrok 文档](https://ngrok.com/docs)
- [LMArena 项目](https://lmarena.ai)
